import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { logger } from '../utils/logger';

// 加载环境变量
dotenv.config();

// 数据库连接URI
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ai_cloud_notes';

// 连接池配置
const connectionOptions: mongoose.ConnectOptions = {
  // 连接池大小配置
  maxPoolSize: parseInt(process.env.DB_MAX_POOL_SIZE || '10'), // 最大连接数
  minPoolSize: parseInt(process.env.DB_MIN_POOL_SIZE || '2'),  // 最小连接数

  // 超时配置
  connectTimeoutMS: parseInt(process.env.DB_CONNECT_TIMEOUT || '30000'), // 30秒连接超时
  socketTimeoutMS: parseInt(process.env.DB_SOCKET_TIMEOUT || '45000'),   // 45秒套接字超时
  serverSelectionTimeoutMS: parseInt(process.env.DB_SERVER_SELECTION_TIMEOUT || '30000'), // 30秒服务器选择超时

  // 心跳检测
  heartbeatFrequencyMS: parseInt(process.env.DB_HEARTBEAT_FREQUENCY || '10000'), // 10秒心跳频率

  // 缓冲配置
  bufferCommands: false, // 禁用命令缓冲

  // 重试配置
  retryWrites: true,
  retryReads: true,

  // 其他配置
  maxIdleTimeMS: parseInt(process.env.DB_MAX_IDLE_TIME || '300000'), // 5分钟最大空闲时间
  waitQueueTimeoutMS: parseInt(process.env.DB_WAIT_QUEUE_TIMEOUT || '10000'), // 10秒等待队列超时
};

/**
 * 连接数据库
 */
export const connectDB = async (): Promise<void> => {
  try {
    // 设置mongoose配置
    mongoose.set('strictQuery', false);

    // 连接数据库
    const conn = await mongoose.connect(MONGODB_URI, connectionOptions);

    logger.info(`MongoDB连接成功: ${conn.connection.host}`);
    logger.info(`连接池配置: 最大${connectionOptions.maxPoolSize}个连接, 最小${connectionOptions.minPoolSize}个连接`);

    // 监听连接事件
    setupConnectionEventListeners();

  } catch (error) {
    logger.error(`MongoDB连接失败: ${error}`);
    process.exit(1);
  }
};

/**
 * 设置连接事件监听器
 */
const setupConnectionEventListeners = (): void => {
  const connection = mongoose.connection;

  // 连接成功事件
  connection.on('connected', () => {
    logger.info('MongoDB连接已建立');
  });

  // 连接断开事件
  connection.on('disconnected', () => {
    logger.warn('MongoDB连接已断开');
  });

  // 连接错误事件
  connection.on('error', (error) => {
    logger.error(`MongoDB连接错误: ${error}`);
  });

  // 重新连接事件
  connection.on('reconnected', () => {
    logger.info('MongoDB已重新连接');
  });

  // 连接关闭事件
  connection.on('close', () => {
    logger.info('MongoDB连接已关闭');
  });

  // 全缓冲事件（当缓冲区满时）
  connection.on('fullsetup', () => {
    logger.info('MongoDB副本集连接已完全建立');
  });

  // 监控连接池状态
  setInterval(async () => {
    try {
      const stats = await connection.db?.admin().serverStatus();
      if (stats) {
        logger.debug(`连接池状态 - 当前连接数: ${connection.readyState}`);
      }
    } catch (error) {
      logger.debug('获取连接池状态失败');
    }
  }, 60000); // 每分钟记录一次连接池状态
};

/**
 * 获取连接池状态
 */
export const getConnectionPoolStatus = () => {
  const connection = mongoose.connection;
  return {
    readyState: connection.readyState,
    host: connection.host,
    port: connection.port,
    name: connection.name,
    collections: Object.keys(connection.collections),
    connectionCount: connection.readyState === 1 ? 'Connected' : 'Disconnected'
  };
};

/**
 * 健康检查
 */
export const healthCheck = async (): Promise<boolean> => {
  try {
    const connection = mongoose.connection;
    if (connection.readyState !== 1) {
      return false;
    }

    // 执行简单的ping操作
    await connection.db?.admin().ping();
    return true;
  } catch (error) {
    logger.error(`数据库健康检查失败: ${error}`);
    return false;
  }
};

/**
 * 关闭数据库连接
 */
export const closeDB = async (): Promise<void> => {
  try {
    await mongoose.connection.close();
    logger.info('MongoDB连接已关闭');
  } catch (error) {
    logger.error(`关闭MongoDB连接失败: ${error}`);
  }
};