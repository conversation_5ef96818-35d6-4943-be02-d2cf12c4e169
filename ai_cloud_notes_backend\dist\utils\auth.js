"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractTokenFromHeader = exports.verifyToken = exports.generateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("../config");
/**
 * 生成JWT令牌
 * @param user 用户对象
 * @returns 包含令牌的对象
 */
const generateToken = (user) => {
    const payload = {
        id: user._id,
        username: user.username,
        email: user.email
    };
    // 生成访问令牌
    const options = { expiresIn: config_1.config.jwt.expiresIn };
    const token = jsonwebtoken_1.default.sign(payload, config_1.config.jwt.secret, options);
    return {
        token,
        expiresIn: config_1.config.jwt.expiresIn
    };
};
exports.generateToken = generateToken;
/**
 * 验证JWT令牌
 * @param token JWT令牌
 * @returns 解析后的令牌载荷
 */
const verifyToken = (token) => {
    try {
        const decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwt.secret);
        return { valid: true, expired: false, decoded };
    }
    catch (error) {
        return {
            valid: false,
            expired: error.name === 'TokenExpiredError',
            decoded: null
        };
    }
};
exports.verifyToken = verifyToken;
/**
 * 从请求头中提取JWT令牌
 * @param authHeader 认证头
 * @returns 提取的令牌
 */
const extractTokenFromHeader = (authHeader) => {
    // 检查认证头是否包含Bearer令牌
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return null;
    }
    // 从Bearer令牌中提取实际的JWT
    const token = authHeader.slice(7);
    return token || null;
};
exports.extractTokenFromHeader = extractTokenFromHeader;
//# sourceMappingURL=auth.js.map