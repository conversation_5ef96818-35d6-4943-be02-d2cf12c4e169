"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.closeRedis = exports.redisHealthCheck = exports.getRedisStatus = exports.getUserSession = exports.setUserSession = exports.isTokenBlacklisted = exports.addTokenToBlacklist = exports.verifyEmailCode = exports.getEmailVerificationCode = exports.setEmailVerificationCode = exports.generateVerificationCode = exports.deleteCache = exports.getCache = exports.setCache = void 0;
const ioredis_1 = __importDefault(require("ioredis"));
const config_1 = require("../config");
const logger_1 = require("./logger");
/**
 * Redis连接池配置
 */
const redisOptions = {
    // 连接池配置
    maxRetriesPerRequest: 3,
    enableReadyCheck: true,
    // 连接配置
    connectTimeout: 10000,
    commandTimeout: 5000,
    lazyConnect: true,
    // 连接池大小
    family: 4, // IPv4
    // 错误处理
    enableOfflineQueue: false,
};
/**
 * Redis客户端实例
 */
const redisClient = new ioredis_1.default(config_1.config.redis.uri, redisOptions);
// 设置Redis事件监听器
redisClient.on('connect', () => {
    logger_1.logger.info('Redis连接已建立');
});
redisClient.on('ready', () => {
    logger_1.logger.info('Redis已准备就绪');
});
redisClient.on('error', (error) => {
    logger_1.logger.error(`Redis连接错误: ${error.message}`);
});
redisClient.on('close', () => {
    logger_1.logger.warn('Redis连接已关闭');
});
redisClient.on('reconnecting', (ms) => {
    logger_1.logger.info(`Redis正在重连，延迟: ${ms}ms`);
});
redisClient.on('end', () => {
    logger_1.logger.warn('Redis连接已结束');
});
/**
 * 生成带前缀的键名
 * @param key 键名
 * @returns 带前缀的键名
 */
const prefixKey = (key) => {
    return `${config_1.config.redis.prefix}${key}`;
};
/**
 * 设置键值对
 * @param key 键名
 * @param value 值
 * @param expire 过期时间（秒）
 */
const setCache = async (key, value, expire = config_1.config.redis.expire) => {
    try {
        const prefixedKey = prefixKey(key);
        await redisClient.set(prefixedKey, value);
        await redisClient.expire(prefixedKey, expire);
        return true;
    }
    catch (error) {
        logger_1.logger.error(`Redis设置缓存失败: ${error.message}`, { stack: error.stack });
        return false;
    }
};
exports.setCache = setCache;
/**
 * 获取键值
 * @param key 键名
 * @returns 值或null
 */
const getCache = async (key) => {
    try {
        const prefixedKey = prefixKey(key);
        return await redisClient.get(prefixedKey);
    }
    catch (error) {
        logger_1.logger.error(`Redis获取缓存失败: ${error.message}`, { stack: error.stack });
        return null;
    }
};
exports.getCache = getCache;
/**
 * 删除键
 * @param key 键名
 */
const deleteCache = async (key) => {
    try {
        const prefixedKey = prefixKey(key);
        await redisClient.del(prefixedKey);
        return true;
    }
    catch (error) {
        logger_1.logger.error(`Redis删除缓存失败: ${error.message}`, { stack: error.stack });
        return false;
    }
};
exports.deleteCache = deleteCache;
/**
 * 生成随机验证码
 * @param length 验证码长度
 * @returns 验证码
 */
const generateVerificationCode = (length = 6) => {
    const chars = '0123456789';
    let code = '';
    for (let i = 0; i < length; i++) {
        code += chars[Math.floor(Math.random() * chars.length)];
    }
    return code;
};
exports.generateVerificationCode = generateVerificationCode;
/**
 * 设置邮箱验证码
 * @param email 邮箱
 * @param code 验证码
 * @param expire 过期时间（秒）
 */
const setEmailVerificationCode = async (email, code, expire = config_1.config.redis.expire) => {
    return await (0, exports.setCache)(`verification:email:${email}`, code, expire);
};
exports.setEmailVerificationCode = setEmailVerificationCode;
/**
 * 获取邮箱验证码
 * @param email 邮箱
 */
const getEmailVerificationCode = async (email) => {
    return await (0, exports.getCache)(`verification:email:${email}`);
};
exports.getEmailVerificationCode = getEmailVerificationCode;
/**
 * 验证邮箱验证码
 * @param email 邮箱
 * @param code 验证码
 */
const verifyEmailCode = async (email, code) => {
    const storedCode = await (0, exports.getEmailVerificationCode)(email);
    if (!storedCode) {
        return false;
    }
    // 验证码匹配检查（忽略大小写）
    const isValid = storedCode.toLowerCase() === code.toLowerCase();
    // 如果验证成功，删除验证码（一次性使用）
    if (isValid) {
        await (0, exports.deleteCache)(`verification:email:${email}`);
    }
    return isValid;
};
exports.verifyEmailCode = verifyEmailCode;
/**
 * 将JWT令牌加入黑名单
 * @param token JWT令牌
 * @param expire 过期时间(秒)，应与令牌剩余有效期一致
 */
const addTokenToBlacklist = async (token, expire) => {
    try {
        return await (0, exports.setCache)(`blacklist:token:${token}`, '1', expire);
    }
    catch (error) {
        logger_1.logger.error(`将令牌加入黑名单失败: ${error.message}`, { stack: error.stack });
        return false;
    }
};
exports.addTokenToBlacklist = addTokenToBlacklist;
/**
 * 检查令牌是否在黑名单中
 * @param token JWT令牌
 */
const isTokenBlacklisted = async (token) => {
    try {
        const result = await (0, exports.getCache)(`blacklist:token:${token}`);
        return result !== null;
    }
    catch (error) {
        logger_1.logger.error(`检查令牌黑名单失败: ${error.message}`, { stack: error.stack });
        return false;
    }
};
exports.isTokenBlacklisted = isTokenBlacklisted;
/**
 * 存储用户会话信息
 * @param userId 用户ID
 * @param sessionData 会话数据
 * @param expire 过期时间(秒)
 */
const setUserSession = async (userId, sessionData, expire = 60 * 60 * 24 * 7) => {
    try {
        return await (0, exports.setCache)(`session:user:${userId}`, JSON.stringify(sessionData), expire);
    }
    catch (error) {
        logger_1.logger.error(`存储用户会话失败: ${error.message}`, { stack: error.stack });
        return false;
    }
};
exports.setUserSession = setUserSession;
/**
 * 获取用户会话信息
 * @param userId 用户ID
 */
const getUserSession = async (userId) => {
    try {
        const data = await (0, exports.getCache)(`session:user:${userId}`);
        return data ? JSON.parse(data) : null;
    }
    catch (error) {
        logger_1.logger.error(`获取用户会话失败: ${error.message}`, { stack: error.stack });
        return null;
    }
};
exports.getUserSession = getUserSession;
/**
 * 获取Redis连接状态
 */
const getRedisStatus = () => {
    return {
        status: redisClient.status,
        connected: redisClient.status === 'ready',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
    };
};
exports.getRedisStatus = getRedisStatus;
/**
 * Redis健康检查
 */
const redisHealthCheck = async () => {
    try {
        const result = await redisClient.ping();
        return result === 'PONG';
    }
    catch (error) {
        logger_1.logger.error(`Redis健康检查失败: ${error}`);
        return false;
    }
};
exports.redisHealthCheck = redisHealthCheck;
/**
 * 关闭Redis连接
 */
const closeRedis = async () => {
    try {
        await redisClient.quit();
        logger_1.logger.info('Redis连接已关闭');
    }
    catch (error) {
        logger_1.logger.error(`关闭Redis连接失败: ${error}`);
    }
};
exports.closeRedis = closeRedis;
exports.default = redisClient;
//# sourceMappingURL=redis.js.map