import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import User from '../models/user.model';
import Note from '../models/note.model';
import Tag from '../models/tag.model';
import { logger } from '../utils/logger';
import mongoose from 'mongoose';
import path from 'path';
import fs from 'fs';
import { promisify } from 'util';
import { config } from '../config';
// @ts-ignore
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';

// 扩展 Request 类型以包含文件字段
declare global {
  namespace Express {
    interface Request {
      file?: Express.Multer.File;
    }
  }
}

// 配置上传路径
const uploadsDir = path.join(__dirname, '../../uploads/avatars');

// 确保上传目录存在
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// 配置 multer 存储
const storage = multer.diskStorage({
  destination: (_req, _file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (_req, file, cb) => {
    const ext = path.extname(file.originalname);
    cb(null, `${uuidv4()}${ext}`);
  }
});

// 创建 multer 实例
export const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB 大小限制
  },
  fileFilter: (_req, file, cb) => {
    // 允许的图片类型
    const allowedTypes = /jpeg|jpg|png|gif/;
    const ext = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (ext && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('仅支持图片文件 (jpeg, jpg, png, gif)'));
    }
  }
});

/**
 * 获取用户个人资料
 * GET /api/users/profile
 */
export const getUserProfile = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    // 查找用户
    const user = await User.findById(userId).select('-passwordHash -resetPasswordToken -resetPasswordExpires');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          message: '用户不存在'
        }
      });
    }

    return res.status(200).json({
      success: true,
      data: {
        user
      }
    });
  } catch (error: any) {
    logger.error(`获取用户资料失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取用户资料失败，请稍后再试'
      }
    });
  }
};

/**
 * 更新用户个人资料
 * PUT /api/users/profile
 */
export const updateUserProfile = async (req: Request, res: Response) => {
  try {
    // 验证请求数据
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { username, email, bio } = req.body;

    // 构建更新对象
    const updateData: any = {};
    if (username) updateData.username = username;
    if (email) updateData.email = email;
    if (bio !== undefined) updateData.bio = bio;

    // 如果要更新用户名或邮箱，需要检查是否已被其他用户使用
    if (username || email) {
      const query: any = { _id: { $ne: userId } };
      if (username) query.username = username;
      if (email) query.email = email;

      const existingUser = await User.findOne(query);
      if (existingUser) {
        return res.status(400).json({
          success: false,
          error: {
            message: '用户名或邮箱已被使用'
          }
        });
      }
    }

    // 更新用户信息
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true }
    ).select('-passwordHash -resetPasswordToken -resetPasswordExpires');

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        error: {
          message: '用户不存在'
        }
      });
    }

    return res.status(200).json({
      success: true,
      message: '个人资料更新成功',
      data: {
        user: updatedUser
      }
    });
  } catch (error: any) {
    logger.error(`更新用户资料失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '更新用户资料失败，请稍后再试'
      }
    });
  }
};

/**
 * 上传用户头像
 * POST /api/users/avatar
 */
export const uploadAvatar = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    // 检查是否有文件上传
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          message: '未提供头像文件'
        }
      });
    }

    // 获取用户信息
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          message: '用户不存在'
        }
      });
    }

    // 如果用户已有头像，删除旧头像文件
    if (user.avatar && user.avatar.startsWith('/uploads/avatars/')) {
      const oldAvatarPath = path.join(__dirname, '../..', user.avatar);
      if (fs.existsSync(oldAvatarPath)) {
        const unlink = promisify(fs.unlink);
        await unlink(oldAvatarPath);
      }
    }

    // 更新用户头像路径
    const avatarPath = `/uploads/avatars/${req.file.filename}`;
    user.avatar = avatarPath;
    await user.save();

    // 构建完整的头像URL
    const avatarUrl = `${config.frontend.url}${avatarPath}`;

    return res.status(200).json({
      success: true,
      message: '头像上传成功',
      data: {
        avatar: avatarPath,
        avatarUrl
      }
    });
  } catch (error: any) {
    logger.error(`上传头像失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '上传头像失败，请稍后再试'
      }
    });
  }
};

/**
 * 获取用户统计信息
 * GET /api/users/stats
 */
export const getUserStats = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    // 获取笔记总数
    const totalNotes = await Note.countDocuments({ owner: userId });

    // 获取收藏笔记数
    const favoriteNotes = await Note.countDocuments({ owner: userId, isFavorite: true });

    // 获取归档笔记数
    const archivedNotes = await Note.countDocuments({ owner: userId, isArchived: true });

    // 获取标签总数
    const totalTags = await Tag.countDocuments({ owner: userId });

    // 计算每月笔记创建趋势
    const monthlyStats = await Note.aggregate([
      {
        $match: {
          owner: new mongoose.Types.ObjectId(userId)
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    return res.status(200).json({
      success: true,
      data: {
        totalNotes,
        favoriteNotes,
        archivedNotes,
        activeNotes: totalNotes - archivedNotes,
        totalTags,
        monthly: monthlyStats.map(item => ({
          year: item._id.year,
          month: item._id.month,
          count: item.count
        }))
      }
    });
  } catch (error: any) {
    logger.error(`获取用户统计信息失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取用户统计信息失败，请稍后再试'
      }
    });
  }
};