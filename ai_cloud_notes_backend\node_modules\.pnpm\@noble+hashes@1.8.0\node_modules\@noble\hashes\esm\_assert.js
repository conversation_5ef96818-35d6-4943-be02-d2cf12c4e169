/**
 * Internal assertion helpers.
 * @module
 * @deprecated
 */
import { abytes as ab, aexists as ae, anumber as an, aoutput as ao, } from "./utils.js";
/** @deprecated Use import from `noble/hashes/utils` module */
export const abytes = ab;
/** @deprecated Use import from `noble/hashes/utils` module */
export const aexists = ae;
/** @deprecated Use import from `noble/hashes/utils` module */
export const anumber = an;
/** @deprecated Use import from `noble/hashes/utils` module */
export const aoutput = ao;
//# sourceMappingURL=_assert.js.map