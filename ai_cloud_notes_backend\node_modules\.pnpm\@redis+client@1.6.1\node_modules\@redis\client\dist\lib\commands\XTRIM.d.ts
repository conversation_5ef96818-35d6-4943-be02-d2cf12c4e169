import { RedisCommandArgument, RedisCommandArguments } from '.';
export declare const FIRST_KEY_INDEX = 1;
interface XTrimOptions {
    strategyModifier?: '=' | '~';
    LIMIT?: number;
}
export declare function transformArguments(key: RedisCommandArgument, strategy: 'MAXLEN' | 'MINID', threshold: number, options?: XTrimOptions): RedisCommandArguments;
export declare function transformReply(): number;
export {};
